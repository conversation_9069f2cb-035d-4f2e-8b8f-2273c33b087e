# SenseFolks Survey Request API

API for handling survey requests with a single endpoint for survey configuration.

## Features

- Retrieve survey configuration by public key
- Caching with Redis for improved performance
- Origin validation based on survey distribution settings
- Comprehensive error handling

## Tech Stack

- Node.js
- TypeScript
- Express.js
- PostgreSQL with Sequelize ORM
- Redis for caching

## Project Structure

```
src/
├── components/           # Feature components
│   └── survey/           # Survey component
│       ├── controllers/  # Request handlers
│       ├── dals/         # Data access layer
│       ├── middlewares/  # Component-specific middlewares
│       ├── models/       # Database models
│       └── routes/       # API routes
├── global/               # Global utilities and configurations
│   ├── helpers/          # Helper functions
│   ├── interfaces/       # TypeScript interfaces
│   ├── middlewares/      # Global middlewares
│   ├── services/         # Shared services
│   └── var/              # Global variables
├── app.ts                # Express application setup
└── index.ts              # Application entry point
```

## Getting Started

### Prerequisites

- Node.js (v14+)
- PostgreSQL
- Redis

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example`
4. Start the development server:
   ```
   npm run dev
   ```

## API Endpoints

### Health Check

```
GET /health
```

### Get Survey by Public Key

```
GET /v1/public-surveys/:publicKey
```

## Database Management

The application supports different database sync modes:

- `RESET_DB=true`: Drops and recreates all tables (development only)
- `ALTER_DB=true`: Alters tables to match models (development only)
- Default: Creates tables if they don't exist, without altering existing ones

## License

ISC
