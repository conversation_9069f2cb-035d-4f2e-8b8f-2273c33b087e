import { Component, Host, h, Prop, State } from '@stencil/core';
import { isValidKey, formatErrorMessage, formatCurrency, isValidPrice } from '../../utils/utils';

// const SURVEY_API_ENDPOINT = 'https://api.sensefolks.com/v1/public-surveys';
const SURVEY_API_ENDPOINT = 'http://localhost:4455/v1/public-surveys';

// const RESPONSE_API_ENDPOINT = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT = 'http://localhost:4466/v1/responses';

enum SurveyStep {
  PRICE_INPUT = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface RespondentDetail {
  label: string;
  value: string;
  required?: boolean;
}

interface SurveyConfig {
  currency: string;
  priceType: string; // 'recurring' or 'non-recurring'
  recurringBasis?: string; // 'monthly' or 'annual' (only if priceType is 'recurring')
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
  respondent_details?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

@Component({
  tag: 'sf-senseprice',
  styleUrl: 'sf-senseprice.css',
  shadow: true,
})
export class SfSenseprice {
  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.PRICE_INPUT;

  @State() priceInput: string = '';

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch survey: ${response.statusText}`);
      }

      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to load survey');
      }

      this.config = data.payload.config;
      // Handle both camelCase and snake_case field names from API
      this.respondentDetails = data.payload.respondentDetails || data.payload.respondent_details || [];
    } catch (error) {
      this.error = formatErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || !isValidPrice(this.priceInput)) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          price: parseFloat(this.priceInput),
          currency: this.config.currency,
          priceType: this.config.priceType,
          recurringBasis: this.config.recurringBasis,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error(`Failed to submit response: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to submit response');
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = formatErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.PRICE_INPUT) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.PRICE_INPUT;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  private handlePriceChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.priceInput = target.value;
  }

  private handleRespondentDetailChange(key: string, value: string) {
    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private isRespondentDetailsValid(): boolean {
    const requiredFields = this.respondentDetails.filter(detail => detail.required);
    return requiredFields.every(field => {
      const value = this.userRespondentDetails[field.value];
      return value && value.trim().length > 0;
    });
  }

  private isPriceValid(): boolean {
    return isValidPrice(this.priceInput) && this.priceInput.trim().length > 0;
  }

  private getFormattedPrice(): string {
    if (!this.isPriceValid() || !this.config) {
      return '';
    }
    return formatCurrency(parseFloat(this.priceInput), this.config.currency);
  }

  private getPriceLabel(): string {
    if (!this.config) {
      return 'Enter your price';
    }

    let label = `Enter your price in ${this.config.currency.toUpperCase()}`;

    if (this.config.priceType === 'recurring' && this.config.recurringBasis) {
      label += ` (${this.config.recurringBasis})`;
    }

    return label;
  }

  private renderPriceInputStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();

    return (
      <div part="step price-input-step">
        <h2 part="heading price-heading">{this.getPriceLabel()}</h2>
        <div part="price-input-container">
          <div part="currency-symbol">{this.config?.currency?.toUpperCase() || '$'}</div>
          <input
            part="input price-input"
            type="number"
            min="0"
            step="0.01"
            value={this.priceInput}
            onInput={e => this.handlePriceChange(e)}
            placeholder="0.00"
          />
          {this.config?.priceType === 'recurring' && this.config?.recurringBasis && (
            <div part="recurring-indicator">/{this.config.recurringBasis}</div>
          )}
        </div>
        {this.isPriceValid() && (
          <div part="price-preview">
            Preview: {this.getFormattedPrice()}
            {this.config?.priceType === 'recurring' && this.config?.recurringBasis &&
              ` per ${this.config.recurringBasis.slice(0, -2)}`
            }
          </div>
        )}
        <div part="button-container">
          <button
            part="button next-button"
            onClick={() => this.nextStep()}
            disabled={!this.isPriceValid()}
          >
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }
