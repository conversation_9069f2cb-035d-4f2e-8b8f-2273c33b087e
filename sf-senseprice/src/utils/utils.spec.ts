import { format } from './utils';

describe('format', () => {
  it('returns empty string for no names defined', () => {
    expect(format(undefined, undefined, undefined)).toEqual('');
  });

  it('formats just first names', () => {
    expect(format('<PERSON>', undefined, undefined)).toEqual('<PERSON>');
  });

  it('formats first and last names', () => {
    expect(format('<PERSON>', undefined, 'Publique')).toEqual('Joseph Publique');
  });

  it('formats first, middle and last names', () => {
    expect(format('<PERSON>', 'Quincy', 'Publique')).toEqual('Joseph Quincy Publique');
  });
});
